"""
Strategy3 - 高级交易策略
架构: 原始行情 → 指标计算 → 模糊推理 → 执行引擎
完全符合无限易Pro架构标准
"""

from typing import Literal, Dict, List, Optional, Tuple
import numpy as np
from collections import deque
import math

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # 技术指标参数
    hull_period: int = Field(default=9, title="HULL周期")
    stc_fast: int = Field(default=23, title="STC快周期")
    stc_slow: int = Field(default=50, title="STC慢周期")
    stc_cycle: int = Field(default=10, title="STC循环周期")
    
    # 交易参数
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 风险控制参数
    stop_loss_pct: float = Field(default=0.02, title="止损百分比")
    take_profit_pct: float = Field(default=0.05, title="止盈百分比")
    
    # 模糊系统参数
    fuzzy_sensitivity: float = Field(default=0.5, title="模糊系统敏感度")


class State(BaseState):
    """状态映射模型"""
    # HULL指标状态
    hull_value: float = Field(default=0, title="HULL当前值")
    hull_prev: float = Field(default=0, title="HULL前值")
    
    # STC指标状态
    stc_value: float = Field(default=0, title="STC当前值")
    stc_signal: float = Field(default=0, title="STC信号线")
    
    # 模糊系统状态
    risk_level: str = Field(default="", title="风险等级")
    action_level: str = Field(default="", title="行动等级")
    confidence: float = Field(default=0, title="置信度")
    
    # K线形态状态
    pattern_detected: str = Field(default="", title="检测到的形态")
    pattern_strength: float = Field(default=0, title="形态强度")
    
    # 波段识别状态
    wave_type: str = Field(default="", title="当前波段类型")
    wave_strength: float = Field(default=0, title="波段强度")


# ==================== 模糊系统 ====================

class TrapezoidalFuzzyNumber:
    """梯形模糊数实现"""
    def __init__(self, a: float, b: float, c: float, d: float):
        """
        初始化梯形模糊数
        
        参数:
            a: 左下角点
            b: 左上角点
            c: 右上角点
            d: 右下角点
        """
        assert a <= b <= c <= d, "Invalid trapezoid parameters"
        self.a, self.b, self.c, self.d = a, b, c, d
        
    def membership(self, x: float) -> float:
        """
        计算隶属度
        
        参数:
            x: 输入值
            
        返回:
            隶属度值 (0-1)
        """
        if x < self.a:
            return 0.0
        elif self.a <= x < self.b:
            return (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return 1.0
        elif self.c < x <= self.d:
            return (self.d - x) / (self.d - self.c)
        else:
            return 0.0
    
    def centroid(self) -> float:
        """
        计算重心去模糊化值
        
        返回:
            重心值
        """
        return (self.a + self.b + self.c + self.d) / 4.0


class FuzzySystem:
    """标准模糊推理系统"""
    def __init__(self, sensitivity: float = 0.5):
        """
        初始化模糊系统
        
        参数:
            sensitivity: 敏感度参数 (0-1)
        """
        self.sensitivity = sensitivity
        
        # 初始化模糊集
        self.stability_sets = self._init_stability_sets()
        self.volatility_sets = self._init_volatility_sets()
        self.profit_sets = self._init_profit_sets()
        
        # 初始化规则库
        self.rules = self._init_rules()
        
        # 风险和行动等级
        self.risk_levels = ["RiskLow", "RiskMedium", "RiskHigh"]
        self.action_levels = ["Conservative", "Normal", "Aggressive", "Stop"]
    
    def _init_stability_sets(self) -> Dict[str, TrapezoidalFuzzyNumber]:
        """初始化稳定性模糊集"""
        return {
            "Low": TrapezoidalFuzzyNumber(0, 0, 0.3, 0.5),
            "Medium": TrapezoidalFuzzyNumber(0.3, 0.5, 0.5, 0.7),
            "High": TrapezoidalFuzzyNumber(0.5, 0.7, 1, 1)
        }
    
    def _init_volatility_sets(self) -> Dict[str, TrapezoidalFuzzyNumber]:
        """初始化波动性模糊集"""
        return {
            "Low": TrapezoidalFuzzyNumber(0, 0, 0.02, 0.05),
            "Medium": TrapezoidalFuzzyNumber(0.02, 0.05, 0.05, 0.1),
            "High": TrapezoidalFuzzyNumber(0.05, 0.1, 1, 1)
        }
    
    def _init_profit_sets(self) -> Dict[str, TrapezoidalFuzzyNumber]:
        """初始化利润模糊集"""
        return {
            "Negative": TrapezoidalFuzzyNumber(-1, -1, -0.02, 0),
            "Neutral": TrapezoidalFuzzyNumber(-0.02, 0, 0, 0.02),
            "Positive": TrapezoidalFuzzyNumber(0, 0.02, 1, 1)
        }
    
    def _init_rules(self) -> List[Dict]:
        """初始化模糊规则库"""
        return [
            {"stability": "Low", "volatility": "Low", "profit": "Neutral", 
             "risk": "RiskLow", "action": "Conservative", "weight": 1.0},
            
            {"stability": "Medium", "volatility": "Low", "profit": "Positive", 
             "risk": "RiskMedium", "action": "Normal", "weight": 1.0},
            
            {"stability": "High", "volatility": "Low", "profit": "Positive", 
             "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0},
            
            {"stability": "High", "volatility": "High", "profit": "Negative", 
             "risk": "RiskHigh", "action": "Stop", "weight": 1.0},
            
            {"stability": "Medium", "volatility": "Medium", "profit": "Neutral", 
             "risk": "RiskMedium", "action": "Normal", "weight": 1.0},
            
            {"stability": "Low", "volatility": "High", "profit": "Negative", 
             "risk": "RiskLow", "action": "Stop", "weight": 1.0}
        ]
    
    def fuzzify(self, inputs: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """
        模糊化输入
        
        参数:
            inputs: 包含stability, volatility, profit的字典
            
        返回:
            各输入变量的隶属度字典
        """
        stability = inputs.get('stability', 0.5)
        volatility = inputs.get('volatility', 0.05)
        profit = inputs.get('profit', 0.0)
        
        memberships = {
            'stability': {k: f.membership(stability) for k, f in self.stability_sets.items()},
            'volatility': {k: f.membership(volatility) for k, f in self.volatility_sets.items()},
            'profit': {k: f.membership(profit) for k, f in self.profit_sets.items()}
        }
        return memberships
    
    def infer(self, memberships: Dict[str, Dict[str, float]]) -> Tuple[str, str, float]:
        """
        模糊推理
        
        参数:
            memberships: 模糊化后的隶属度字典
            
        返回:
            (风险等级, 行动等级, 置信度)
        """
        stability_mem = memberships['stability']
        volatility_mem = memberships['volatility']
        profit_mem = memberships['profit']
        
        # 计算综合风险等级
        risk_scores = {}
        for risk_level in self.risk_levels:
            score = 0.0
            for rule in self.rules:
                if rule['risk'] == risk_level:
                    s_match = stability_mem.get(rule['stability'], 0)
                    v_match = volatility_mem.get(rule['volatility'], 0)
                    p_match = profit_mem.get(rule['profit'], 0)
                    
                    # 应用敏感度调整
                    rule_strength = min(s_match, v_match, p_match) * rule.get('weight', 1.0)
                    rule_strength = rule_strength * (1 + (self.sensitivity - 0.5))
                    
                    score += rule_strength
            
            risk_scores[risk_level] = score
        
        # 计算综合行动等级
        action_scores = {}
        for action_level in self.action_levels:
            score = 0.0
            for rule in self.rules:
                if rule['action'] == action_level:
                    s_match = stability_mem.get(rule['stability'], 0)
                    v_match = volatility_mem.get(rule['volatility'], 0)
                    p_match = profit_mem.get(rule['profit'], 0)
                    
                    # 应用敏感度调整
                    rule_strength = min(s_match, v_match, p_match) * rule.get('weight', 1.0)
                    rule_strength = rule_strength * (1 + (self.sensitivity - 0.5))
                    
                    score += rule_strength
            
            action_scores[action_level] = score
        
        # 选择最高分数的风险等级和行动等级
        best_risk = max(risk_scores.items(), key=lambda x: x[1])[0]
        best_action = max(action_scores.items(), key=lambda x: x[1])[0]
        
        # 计算置信度
        confidence = max(max(risk_scores.values()), max(action_scores.values()))
        
        return best_risk, best_action, confidence


# =============== 技术指标系统 ====================

class HullMovingAverage:
    """改进版Hull移动平均线"""
    def __init__(self, period: int = 9):
        """
        初始化Hull移动平均线
        
        参数:
            period: 周期
        """
        self.period = period
        self.half_period = max(1, period // 2)
        self.sqrt_period = max(1, int(math.sqrt(period)))
        
        # 价格历史缓冲区
        self.prices = deque(maxlen=period * 2)
        
        # 中间计算结果缓冲区
        self.wma_half_history = deque(maxlen=100)
        self.wma_full_history = deque(maxlen=100)
        self.hull_raw_history = deque(maxlen=self.sqrt_period * 2)
        self.hull_final_history = deque(maxlen=100)
        
        # 优化的权重预计算
        self.weights_half = self._calculate_weights(self.half_period)
        self.weights_full = self._calculate_weights(self.period)
        self.weights_sqrt = self._calculate_weights(self.sqrt_period)
        
        # 状态标志
        self._is_ready = False
        self._warmup_count = 0
        self._min_warmup = period + self.sqrt_period
    
    def _calculate_weights(self, period: int) -> np.ndarray:
        """
        预计算WMA权重
        
        参数:
            period: 周期
            
        返回:
            权重数组
        """
        weights = np.arange(1, period + 1, dtype=np.float64)
        return weights / np.sum(weights)
    
    def _calculate_wma_optimized(self, data: List[float], weights: np.ndarray) -> Optional[float]:
        """
        优化的WMA计算
        
        参数:
            data: 价格数据
            weights: 权重数组
            
        返回:
            WMA值
        """
        if len(data) < len(weights):
            return None
        
        # 使用最新的数据点
        recent_data = np.array(data[-len(weights):], dtype=np.float64)
        return np.dot(recent_data, weights)
    
    def _apply_smoothing_filter(self, value: float, history: deque, alpha: float = 0.1) -> float:
        """
        应用平滑滤波器减少噪声
        
        参数:
            value: 当前值
            history: 历史值队列
            alpha: 平滑系数
            
        返回:
            平滑后的值
        """
        if len(history) == 0:
            return value
        
        # 简单的指数平滑
        prev_value = history[-1]
        return alpha * value + (1 - alpha) * prev_value
    
    def update(self, price: float) -> None:
        """
        更新HULL值
        
        参数:
            price: 最新价格
        """
        if not isinstance(price, (int, float)) or math.isnan(price) or math.isinf(price):
            return
        
        self.prices.append(float(price))
        self._warmup_count += 1
        
        # 需要足够的数据才能开始计算
        if len(self.prices) < self.period:
            return
        
        try:
            # 步骤1: 计算WMA(period/2)
            wma_half = self._calculate_wma_optimized(list(self.prices), self.weights_half)
            if wma_half is not None:
                wma_half = self._apply_smoothing_filter(wma_half, self.wma_half_history, 0.05)
                self.wma_half_history.append(wma_half)
            
            # 步骤2: 计算WMA(period)
            wma_full = self._calculate_wma_optimized(list(self.prices), self.weights_full)
            if wma_full is not None:
                wma_full = self._apply_smoothing_filter(wma_full, self.wma_full_history, 0.05)
                self.wma_full_history.append(wma_full)
            
            # 步骤3: 计算原始HULL值
            if wma_half is not None and wma_full is not None:
                hull_raw = 2.0 * wma_half - wma_full
                self.hull_raw_history.append(hull_raw)
                
                # 步骤4: 计算最终HULL值
                if len(self.hull_raw_history) >= self.sqrt_period:
                    hull_final = self._calculate_wma_optimized(
                        list(self.hull_raw_history), 
                        self.weights_sqrt
                    )
                    
                    if hull_final is not None:
                        # 应用最终平滑
                        hull_final = self._apply_smoothing_filter(
                            hull_final, 
                            self.hull_final_history, 
                            0.03
                        )
                        self.hull_final_history.append(hull_final)
                        
                        # 检查是否已经就绪
                        if self._warmup_count >= self._min_warmup:
                            self._is_ready = True
        
        except Exception:
            # 静默处理计算错误，保持稳定性
            pass
    
    def get_hull(self) -> Tuple[Optional[float], Optional[float]]:
        """
        获取当前HULL值
        
        返回:
            (当前值, 前一个值)
        """
        if not self._is_ready or len(self.hull_final_history) < 2:
            return None, None
        return self.hull_final_history[-1], self.hull_final_history[-2]
    
    def get_trend(self) -> Optional[str]:
        """
        获取趋势方向
        
        返回:
            趋势方向 ("BULLISH", "BEARISH", "NEUTRAL")
        """
        if not self._is_ready or len(self.hull_final_history) < 3:
            return None
        
        current = self.hull_final_history[-1]
        prev = self.hull_final_history[-2]
        prev2 = self.hull_final_history[-3]
        
        # 基于斜率判断趋势
        short_slope = current - prev
        long_slope = current - prev2
        
        if short_slope > 0 and long_slope > 0:
            return "BULLISH"
        elif short_slope < 0 and long_slope < 0:
            return "BEARISH"
        else:
            return "NEUTRAL"
    
    def is_ready(self) -> bool:
        """
        检查指标是否就绪
        
        返回:
            是否就绪
        """
        return self._is_ready and len(self.hull_final_history) > 0


class SchaffTrendCycle:
    """Schaff趋势周期指标"""
    def __init__(self, fast_period: int = 23, slow_period: int = 50, cycle_period: int = 10):
        """
        初始化Schaff趋势周期指标
        
        参数:
            fast_period: 快速周期
            slow_period: 慢速周期
            cycle_period: 循环周期
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.cycle_period = cycle_period
        
        # 使用deque实现高效循环缓冲区
        self.prices = deque(maxlen=200)
        self.macd_line = deque(maxlen=100)
        self.stoch1 = deque(maxlen=50)
        self.stoch2 = deque(maxlen=50)
        self.stc_final = deque(maxlen=30)
        self.stc_signal = deque(maxlen=20)
        
    def ema(self, data: List[float], period: int) -> Optional[List[float]]:
        """
        指数移动平均
        
        参数:
            data: 价格数据
            period: 周期
            
        返回:
            EMA值列表
        """
        if len(data) < period:
            return None
        
        alpha = 2.0 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema = alpha * data[i] + (1 - alpha) * ema_values[-1]
            ema_values.append(ema)
        
        return ema_values
    
    def update(self, price: float) -> None:
        """
        更新STC值
        
        参数:
            price: 最新价格
        """
        self.prices.append(price)
        
        if len(self.prices) < self.slow_period:
            return
        
        # 计算MACD线
        fast_ema = self.ema(list(self.prices), self.fast_period)
        slow_ema = self.ema(list(self.prices), self.slow_period)
        
        if fast_ema and slow_ema:
            # 取最新值计算MACD
            macd_value = fast_ema[-1] - slow_ema[-1]
            self.macd_line.append(macd_value)
            
            # 计算Stoch1
            if len(self.macd_line) >= self.cycle_period:
                stoch1_value = self._calculate_stochastic_single(list(self.macd_line), self.cycle_period)
                if stoch1_value is not None:
                    self.stoch1.append(stoch1_value)
                    
                    # 计算Stoch2
                    if len(self.stoch1) >= self.cycle_period:
                        stoch2_value = self._calculate_stochastic_single(list(self.stoch1), self.cycle_period)
                        if stoch2_value is not None:
                            self.stoch2.append(stoch2_value)
                            
                            # 计算最终STC值
                            if len(self.stoch2) >= 3:
                                stc_smoothed = self.ema(list(self.stoch2), 3)
                                if stc_smoothed:
                                    self.stc_final.append(stc_smoothed[-1])
                                    
                                    # 计算STC信号线
                                    if len(self.stc_final) >= 3:
                                        signal_values = self.ema(list(self.stc_final), 3)
                                        if signal_values:
                                            self.stc_signal.append(signal_values[-1])
    
    def _calculate_stochastic_single(self, data: List[float], period: int) -> Optional[float]:
        """
        计算单个随机指标值
        
        参数:
            data: 价格数据
            period: 周期
            
        返回:
            随机值
        """
        if len(data) < period:
            return None
        
        window = data[-period:]
        highest = np.max(window)
        lowest = np.min(window)
        
        if highest == lowest:
            return 50.0
        else:
            return 100 * (data[-1] - lowest) / (highest - lowest)
    
    def get_stc(self) -> Tuple[Optional[float], Optional[float]]:
        """
        获取当前STC值
        
        返回:
            (STC值, 信号线值)
        """
        if len(self.stc_final) >= 2 and len(self.stc_signal) >= 1:
            return self.stc_final[-1], self.stc_signal[-1]
        return None, None
    
    def is_ready(self) -> bool:
        """
        检查指标是否就绪
        
        返回:
            是否就绪
        """
        return len(self.stc_final) > 2 and len(self.stc_signal) > 1


class CandlePatternRecognizer:
    """K线形态识别器"""
    def __init__(self, window_size: int = 10):
        """
        初始化K线形态识别器
        
        参数:
            window_size: 窗口大小
        """
        self.window_size = window_size
        self.kline_buffer = deque(maxlen=window_size)
        
    def update(self, kline: KLineData) -> None:
        """
        更新K线数据
        
        参数:
            kline: K线数据
        """
        self.kline_buffer.append({
            'open': kline.open,
            'high': kline.high,
            'low': kline.low,
            'close': kline.close,
            'volume': kline.volume
        })
    
    def recognize_patterns(self) -> Dict[str, bool]:
        """
        识别关键K线形态
        
        返回:
            形态识别结果字典
        """
        if len(self.kline_buffer) < 3:  # 至少需要3根K线
            return {}
        
        features = {}
        current = self.kline_buffer[-1]
        prev = self.kline_buffer[-2]
        prev2 = self.kline_buffer[-3]
        
        # 单K线形态
        features['hammer'] = self._is_hammer(current)
        features['shooting_star'] = self._is_shooting_star(current)
        features['marubozu'] = self._is_marubozu(current)
        
        # 双K线形态
        features['bullish_engulfing'] = self._is_bullish_engulfing(prev, current)
        features['bearish_engulfing'] = self._is_bearish_engulfing(prev, current)
        
        # 三K线形态
        features['three_white_soldiers'] = self._is_three_white_soldiers(prev2, prev, current)
        features['three_black_crows'] = self._is_three_black_crows(prev2, prev, current)
        
        # 缺口识别
        features['gap_up'] = self._is_gap_up(prev, current)
        features['gap_down'] = self._is_gap_down(prev, current)
        
        return features
    
    def _is_hammer(self, candle: Dict) -> bool:
        """
        识别锤子线 (底部反转信号)
        
        参数:
            candle: K线数据
            
        返回:
            是否为锤子线
        """
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        lower_shadow = min(candle['open'], candle['close']) - candle['low']
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        
        if total_range == 0 or body == 0:
            return False
        
        # 锤子线条件：长下影线，短上影线，小实体
        return (lower_shadow >= 2 * body and 
                upper_shadow <= max(body * 0.5, total_range * 0.1) and
                body / total_range >= 0.05)
    
    def _is_shooting_star(self, candle: Dict) -> bool:
        """
        识别射击之星 (顶部反转信号)
        
        参数:
            candle: K线数据
            
        返回:
            是否为射击之星
        """
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        
        return body > 0 and upper_shadow >= 2 * body and (min(candle['open'], candle['close']) - candle['low']) <= body * 0.3
    
    def _is_marubozu(self, candle: Dict) -> bool:
        """
        识别光头光脚大阳/阴线
        
        参数:
            candle: K线数据
            
        返回:
            是否为光头光脚线
        """
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        return body > 0 and (body / total_range) > 0.9  # 实体占比90%以上
    
    def _is_bullish_engulfing(self, prev: Dict, current: Dict) -> bool:
        """
        看涨吞没形态
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为看涨吞没形态
        """
        # 前一根是阴线，当前是阳线
        prev_is_bearish = prev['close'] < prev['open']
        current_is_bullish = current['close'] > current['open']
        
        # 当前K线完全吞没前一根K线
        engulfs_completely = (current['open'] <= prev['close'] and 
                             current['close'] >= prev['open'])
        
        # 增加容错机制 - 允许10%的价格偏差
        tolerance = abs(prev['close'] - prev['open']) * 0.1
        engulfs_with_tolerance = (current['open'] <= prev['close'] + tolerance and 
                                 current['close'] >= prev['open'] - tolerance)
        
        return (prev_is_bearish and current_is_bullish and 
                (engulfs_completely or engulfs_with_tolerance))
    
    def _is_bearish_engulfing(self, prev: Dict, current: Dict) -> bool:
        """
        看跌吞没形态
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为看跌吞没形态
        """
        # 前一根是阳线，当前是阴线
        prev_is_bullish = prev['close'] > prev['open']
        current_is_bearish = current['close'] < current['open']
        
        # 当前K线完全吞没前一根K线
        engulfs_completely = (current['open'] >= prev['close'] and 
                             current['close'] <= prev['open'])
        
        # 增加容错机制 - 允许10%的价格偏差
        tolerance = abs(prev['close'] - prev['open']) * 0.1
        engulfs_with_tolerance = (current['open'] >= prev['close'] - tolerance and 
                                 current['close'] <= prev['open'] + tolerance)
        
        return (prev_is_bullish and current_is_bearish and 
                (engulfs_completely or engulfs_with_tolerance))
    
    def _is_three_white_soldiers(self, prev2: Dict, prev: Dict, current: Dict) -> bool:
        """
        三白兵形态 (连续三根阳线)
        
        参数:
            prev2: 前前一根K线
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为三白兵形态
        """
        return (prev2['close'] > prev2['open'] and 
                prev['close'] > prev['open'] and 
                current['close'] > current['open'] and
                prev['open'] > prev2['close'] and
                current['open'] > prev['close'])
    
    def _is_three_black_crows(self, prev2: Dict, prev: Dict, current: Dict) -> bool:
        """
        三黑鸦形态 (连续三根阴线)
        
        参数:
            prev2: 前前一根K线
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为三黑鸦形态
        """
        return (prev2['close'] < prev2['open'] and 
                prev['close'] < prev['open'] and 
                current['close'] < current['open'] and
                prev['open'] < prev2['close'] and
                current['open'] < prev['close'])
    
    def _is_gap_up(self, prev: Dict, current: Dict) -> bool:
        """
        向上跳空识别
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为向上跳空
        """
        gap_size = current['low'] - prev['high']
        if gap_size <= 0:
            return False
        
        # 计算相对跳空幅度
        avg_price = (prev['high'] + prev['low']) / 2
        relative_gap = gap_size / avg_price if avg_price > 0 else 0
        
        # 跳空幅度需要超过0.1%才认为是有效跳空
        return relative_gap > 0.001
    
    def _is_gap_down(self, prev: Dict, current: Dict) -> bool:
        """
        向下跳空识别
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为向下跳空
        """
        gap_size = prev['low'] - current['high']
        if gap_size <= 0:
            return False
        
        # 计算相对跳空幅度
        avg_price = (prev['high'] + prev['low']) / 2
        relative_gap = gap_size / avg_price if avg_price > 0 else 0
        
        # 跳空幅度需要超过0.1%才认为是有效跳空
        return relative_gap > 0.001
    
    def is_ready(self) -> bool:
        """
        检查形态识别器是否就绪
        
        返回:
            是否就绪
        """
        return len(self.kline_buffer) >= 3


class WavePatternRecognizer:
    """波段识别器"""
    def __init__(self, lookback_period: int = 50):
        """
        初始化波段识别器
        
        参数:
            lookback_period: 回溯周期
        """
        self.lookback_period = lookback_period
        self.price_history = deque(maxlen=lookback_period)
        self.hull_history = deque(maxlen=lookback_period)
        self.stc_history = deque(maxlen=lookback_period)
        self.swing_points = deque(maxlen=20)  # 存储最近的20个摆动点
        self.current_wave = {
            'type': 'unknown',  # 'uptrend', 'downtrend', 'consolidation', 'unknown'
            'start_price': 0,
            'start_time': None,
            'duration': 0,
            'strength': 0,
            'swing_count': 0
        }
        self.waves = deque(maxlen=10)  # 存储最近的10个波段
    
    def update(self, price: float, hull_value: float, stc_value: float, timestamp=None) -> None:
        """
        更新波段识别器
        
        参数:
            price: 价格
            hull_value: HULL值
            stc_value: STC值
            timestamp: 时间戳
        """
        # 添加新数据
        self.price_history.append(price)
        self.hull_history.append(hull_value)
        self.stc_history.append(stc_value)
        
        # 至少需要10个数据点才能开始识别
        if len(self.price_history) < 10:
            return
        
        # 识别摆动点
        self._identify_swing_points()
        
        # 识别当前波段
        self._identify_current_wave(timestamp)
    
    def _identify_swing_points(self) -> None:
        """识别价格摆动点（高点和低点）"""
        # 需要至少5个点来识别摆动点
        if len(self.price_history) < 5:
            return
        
        prices = list(self.price_history)
        # 检查倒数第3个点是否是局部高点或低点
        idx = -3  # 倒数第3个点
        
        # 局部高点: 前后两个点都比它低
        if prices[idx] > prices[idx-1] and prices[idx] > prices[idx-2] and \
           prices[idx] > prices[idx+1] and prices[idx] > prices[idx+2]:
            # 确认是否为新的摆动点
            if not self.swing_points or self.swing_points[-1]['type'] != 'high':
                self.swing_points.append({
                    'type': 'high',
                    'price': prices[idx],
                    'index': len(prices) + idx,
                    'confirmed': True
                })
        
        # 局部低点: 前后两个点都比它高
        elif prices[idx] < prices[idx-1] and prices[idx] < prices[idx-2] and \
             prices[idx] < prices[idx+1] and prices[idx] < prices[idx+2]:
            # 确认是否为新的摆动点
            if not self.swing_points or self.swing_points[-1]['type'] != 'low':
                self.swing_points.append({
                    'type': 'low',
                    'price': prices[idx],
                    'index': len(prices) + idx,
                    'confirmed': True
                })
    
    def _identify_current_wave(self, timestamp) -> None:
        """
        识别当前波段
        
        参数:
            timestamp: 时间戳
        """
        if len(self.swing_points) < 2:
            return
        
        # 获取最近的摆动点
        recent_swings = list(self.swing_points)[-4:]
        
        # 计算波段类型
        if len(recent_swings) >= 2:
            # 检查最近的两个摆动点
            last_swing = recent_swings[-1]
            prev_swing = recent_swings[-2]
            
            # 上升趋势: 低点后高点
            if last_swing['type'] == 'high' and prev_swing['type'] == 'low':
                wave_type = 'uptrend'
                strength = (last_swing['price'] - prev_swing['price']) / prev_swing['price']
            
            # 下降趋势: 高点后低点
            elif last_swing['type'] == 'low' and prev_swing['type'] == 'high':
                wave_type = 'downtrend'
                strength = (prev_swing['price'] - last_swing['price']) / prev_swing['price']
            
            # 盘整: 同类型摆动点
            else:
                wave_type = 'consolidation'
                strength = abs(last_swing['price'] - prev_swing['price']) / prev_swing['price']
            
            # 更新当前波段
            if wave_type != self.current_wave['type']:
                # 保存前一个波段
                if self.current_wave['type'] != 'unknown':
                    self.waves.append(self.current_wave.copy())
                
                # 创建新波段
                self.current_wave = {
                    'type': wave_type,
                    'start_price': prev_swing['price'],
                    'start_time': timestamp,
                    'duration': last_swing['index'] - prev_swing['index'],
                    'strength': strength,
                    'swing_count': 1
                }
            else:
                # 更新现有波段
                self.current_wave['duration'] = last_swing['index'] - prev_swing['index'] + self.current_wave['duration']
                self.current_wave['strength'] = (self.current_wave['strength'] + strength) / 2
                self.current_wave['swing_count'] += 1
    
    def get_wave_info(self) -> Dict:
        """
        获取当前波段信息
        
        返回:
            波段信息字典
        """
        return {
            'type': self.current_wave['type'],
            'strength': self.current_wave['strength'],
            'duration': self.current_wave['duration'],
            'swing_count': self.current_wave['swing_count']
        }
    
    def is_ready(self) -> bool:
        """
        检查波段识别器是否就绪
        
        返回:
            是否就绪
        """
        return len(self.swing_points) >= 2


def log(msg, level="INFO"):
    print(f"[{level}] {msg}")

# ================ 主策略类 ==================

class Strategy3(BaseStrategy):
    """Strategy3 - 高级交易策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        """参数表"""

        self.state_map = State()
        """状态表"""

        # 信号标志
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        # 当前行情
        self.tick = None
        self.order_id = None
        self.signal_price = 0
        
        # 技术指标
        self.hull_calculator = None
        self.stc_calculator = None
        self.pattern_recognizer = None
        self.wave_recognizer = None
        self.fuzzy_system = None
        
        # 历史数据
        self.hull_prev = 0
        self.stc_prev = 0

        # 内置自适应参数字典集
        self.adaptive_config = {
            'weights': {
                'indicator': 0.6,
                'pattern': 0.15,
                'wave': 0.15,
                'fuzzy': 0.1
            },
            'thresholds': {
                'high_vol': 0.6,
                'low_vol': 0.4
            },
            'min_order_volume': 1
        }
        self.signal_threshold = self.adaptive_config['thresholds']['low_vol']
        # 信号模块胜率统计
        self.signal_stats = {
            'indicator': {'win': 1, 'total': 2},
            'pattern': {'win': 1, 'total': 2},
            'wave': {'win': 1, 'total': 2},
            'fuzzy': {'win': 1, 'total': 2}
        }
        self.last_trade_signals = None  # 记录每次开仓时的信号来源

    @property
    def main_indicator_data(self) -> Dict[str, float]:
        """主图指标"""
        return {
            f"HULL{self.params_map.hull_period}": self.state_map.hull_value,
            "STC": self.state_map.stc_value,
            "STC_Signal": self.state_map.stc_signal
        }

    @property
    def sub_indicator_data(self) -> Dict[str, float]:
        """副图指标"""
        return {
            "Confidence": self.state_map.confidence,
            "WaveStrength": self.state_map.wave_strength,
            "PatternStrength": self.state_map.pattern_strength
        }

    def on_tick(self, tick: TickData) -> None:
        """
        Tick数据回调
        
        参数:
            tick: Tick数据
        """
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """
        撤单推送回调
        
        参数:
            order: 订单数据
        """
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """
        成交回调
        
        参数:
            trade: 成交数据
            log: 是否记录日志
        """
        super().on_trade(trade, log)
        self.order_id = None
        # 假设trade.profit为本次平仓盈亏（需根据实际TradeData结构调整）
        if hasattr(trade, 'profit'):
            self.update_signal_stats(trade.profit)

    def on_start(self) -> None:
        """策略启动"""
        # 初始化技术指标（提前）
        self.hull_calculator = HullMovingAverage(self.params_map.hull_period)
        self.stc_calculator = SchaffTrendCycle(
            self.params_map.stc_fast,
            self.params_map.stc_slow,
            self.params_map.stc_cycle
        )
        self.pattern_recognizer = CandlePatternRecognizer()
        self.wave_recognizer = WavePatternRecognizer()
        self.fuzzy_system = FuzzySystem(self.params_map.fuzzy_sensitivity)

        # 初始化K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        # 重置信号
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None

        self.update_status_bar()

    def on_stop(self) -> None:
        """策略停止"""
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """
        接受 K 线回调
        
        参数:
            kline: K线数据
        """
        # 更新技术指标
        self.update_indicators(kline)
        
        # 计算信号
        self.calc_signal(kline)
        
        # 信号执行
        self.exec_signal()
        
        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """
        实时K线回调
        
        参数:
            kline: K线数据
        """
        # 更新技术指标
        self.update_indicators(kline)
        
        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        self.update_status_bar()

    def update_signal_stats(self, profit):
        """
        在平仓时调用，更新信号模块胜率统计
        profit: 本次平仓盈亏（正为盈利，负为亏损）
        """
        if self.last_trade_signals:
            for key in self.last_trade_signals:
                self.signal_stats[key]['total'] += 1
                if profit > 0:
                    self.signal_stats[key]['win'] += 1
            log(f"信号统计更新: {self.signal_stats}")

    def update_weights(self):
        """
        根据信号模块历史胜率动态调整权重
        """
        for key in self.signal_stats:
            stat = self.signal_stats[key]
            win_rate = stat['win'] / stat['total'] if stat['total'] > 0 else 0.5
            # 权重在0.1~0.5之间动态调整
            self.adaptive_config['weights'][key] = 0.1 + 0.4 * win_rate
        # 归一化
        total = sum(self.adaptive_config['weights'].values())
        for key in self.adaptive_config['weights']:
            self.adaptive_config['weights'][key] /= total
        log(f"动态权重: {self.adaptive_config['weights']}")

    def adjust_for_market_structure(self):
        """
        根据波段识别的市场结构动态调整权重
        """
        wave_type = self.state_map.wave_type
        w = self.adaptive_config['weights']
        # 先还原到基础权重
        base = {'indicator': 0.6, 'pattern': 0.15, 'wave': 0.15, 'fuzzy': 0.1}
        w.update(base)
        if wave_type == 'uptrend' or wave_type == 'downtrend':
            w['indicator'] += 0.1
            w['wave'] += 0.1
            w['pattern'] -= 0.1
        elif wave_type == 'consolidation':
            w['pattern'] += 0.1
            w['indicator'] -= 0.1
        # 归一化
        total = sum(w.values())
        for key in w:
            w[key] = max(0.05, w[key] / total)
        log(f"市场结构权重: {w}")

    def fuse_signals(self) -> dict:
        """
        融合各类信号，输出最终信号及置信度
        返回: {'buy': 置信度, 'sell': 置信度}
        """
        w = self.adaptive_config['weights']
        # 1. 技术指标信号
        hull_up = self.state_map.hull_value > self.hull_prev if self.hull_prev is not None else False
        stc_up = self.state_map.stc_value > self.stc_prev if self.stc_prev is not None else False
        indicator_buy = 1.0 if hull_up and stc_up else 0.0
        indicator_sell = 1.0 if not hull_up and not stc_up else 0.0
        # 2. K线形态信号
        pattern_strength = self.state_map.pattern_strength
        pattern_buy = pattern_strength if self.state_map.pattern_detected in ['bullish_engulfing', 'hammer', 'three_white_soldiers', 'gap_up'] else 0.0
        pattern_sell = pattern_strength if self.state_map.pattern_detected in ['bearish_engulfing', 'shooting_star', 'three_black_crows', 'gap_down'] else 0.0
        # 3. 波段信号
        wave_type = self.state_map.wave_type
        wave_strength = self.state_map.wave_strength
        wave_buy = wave_strength if wave_type == 'uptrend' else 0.0
        wave_sell = wave_strength if wave_type == 'downtrend' else 0.0
        # 4. 模糊系统信号
        fuzzy_action = self.state_map.action_level
        fuzzy_conf = self.state_map.confidence
        fuzzy_buy = fuzzy_conf if fuzzy_action == 'Aggressive' else 0.0
        fuzzy_sell = fuzzy_conf if fuzzy_action == 'Stop' else 0.0
        # 5. 融合（自适应加权平均）
        buy_score = w['indicator'] * indicator_buy + w['pattern'] * pattern_buy + w['wave'] * wave_buy + w['fuzzy'] * fuzzy_buy
        sell_score = w['indicator'] * indicator_sell + w['pattern'] * pattern_sell + w['wave'] * wave_sell + w['fuzzy'] * fuzzy_sell
        # 记录本次信号来源
        self.last_trade_signals = []
        if indicator_buy > 0: self.last_trade_signals.append('indicator')
        if pattern_buy > 0: self.last_trade_signals.append('pattern')
        if wave_buy > 0: self.last_trade_signals.append('wave')
        if fuzzy_buy > 0: self.last_trade_signals.append('fuzzy')
        log(f"信号融合得分: buy={buy_score:.3f}, sell={sell_score:.3f}, 阈值={self.signal_threshold:.2f}")
        return {'buy': buy_score, 'sell': sell_score}

    def adjust_params(self):
        """
        综合自适应调整：动态权重、市场结构、风控参数
        """
        self.update_weights()
        self.adjust_for_market_structure()
        recent_volatility = self.get_recent_volatility()
        # 动态调整阈值
        if recent_volatility > 0.03:
            self.signal_threshold = self.adaptive_config['thresholds']['high_vol']
        else:
            self.signal_threshold = self.adaptive_config['thresholds']['low_vol']
        log(f"自适应参数: 波动率={recent_volatility:.4f}, 阈值={self.signal_threshold:.2f}")

    def adjust_risk_params(self, signal_strength):
        """
        信号强度与风控参数联动
        """
        if signal_strength > 0.7:
            self.params_map.stop_loss_pct = 0.03
            self.params_map.take_profit_pct = 0.1
        else:
            self.params_map.stop_loss_pct = 0.015
            self.params_map.take_profit_pct = 0.04
        log(f"风控参数: 止损={self.params_map.stop_loss_pct:.3f}, 止盈={self.params_map.take_profit_pct:.3f}")

    def get_recent_volatility(self, window: int = 10) -> float:
        """
        计算最近window根K线的平均波动率
        """
        if not hasattr(self.wave_recognizer, 'price_history') or len(self.wave_recognizer.price_history) < window:
            return 0.02
        prices = list(self.wave_recognizer.price_history)[-window:]
        returns = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] != 0]
        return sum(returns) / len(returns) if returns else 0.02

    def update_indicators(self, kline: KLineData) -> None:
        """
        更新技术指标
        参数:
            kline: K线数据
        """
        # 更新HULL指标
        self.hull_calculator.update(kline.close)
        hull_value, hull_prev = self.hull_calculator.get_hull()
        if hull_value is not None:
            self.state_map.hull_prev = self.state_map.hull_value
            self.state_map.hull_value = hull_value
            self.hull_prev = hull_prev
        # 更新STC指标
        self.stc_calculator.update(kline.close)
        stc_value, stc_signal = self.stc_calculator.get_stc()
        if stc_value is not None:
            self.stc_prev = self.state_map.stc_value
            self.state_map.stc_value = stc_value
            self.state_map.stc_signal = stc_signal
        # 更新K线形态识别
        self.pattern_recognizer.update(kline)
        if self.pattern_recognizer.is_ready():
            patterns = self.pattern_recognizer.recognize_patterns()
            strongest_pattern = None
            max_strength = 0
            for pattern, is_present in patterns.items():
                if is_present:
                    strength = 0
                    if pattern in ['bullish_engulfing', 'bearish_engulfing']:
                        strength = 0.8
                    elif pattern in ['hammer', 'shooting_star']:
                        strength = 0.7
                    elif pattern in ['three_white_soldiers', 'three_black_crows']:
                        strength = 0.9
                    elif pattern in ['gap_up', 'gap_down']:
                        strength = 0.6
                    elif pattern == 'marubozu':
                        strength = 0.5
                    if strength > max_strength:
                        max_strength = strength
                        strongest_pattern = pattern
            if strongest_pattern:
                self.state_map.pattern_detected = strongest_pattern
                self.state_map.pattern_strength = max_strength
            else:
                self.state_map.pattern_detected = ""
                self.state_map.pattern_strength = 0
        # 更新波段识别
        if hull_value is not None and stc_value is not None:
            timestamp = getattr(kline, "timestamp", None)
            self.wave_recognizer.update(kline.close, hull_value, stc_value, timestamp)
            if self.wave_recognizer.is_ready():
                wave_info = self.wave_recognizer.get_wave_info()
                self.state_map.wave_type = wave_info['type']
                self.state_map.wave_strength = wave_info['strength']
        # 更新模糊系统
        if self.hull_calculator.is_ready() and self.stc_calculator.is_ready():
            hull_trend = self.hull_calculator.get_trend()
            stability = 0.5
            if hull_trend == "BULLISH":
                stability = 0.7
            elif hull_trend == "BEARISH":
                stability = 0.3
            if len(self.wave_recognizer.price_history) >= 10:
                prices = list(self.wave_recognizer.price_history)[-10:]
                price_changes = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] != 0]
                volatility = sum(price_changes) / len(price_changes) if price_changes else 0.05
            else:
                volatility = 0.05
            if len(self.stc_calculator.stc_final) >= 3:
                stc_values = list(self.stc_calculator.stc_final)[-3:]
                profit_trend = (stc_values[-1] - stc_values[0]) / 100.0
            else:
                profit_trend = 0.0
            fuzzy_inputs = {
                'stability': stability,
                'volatility': volatility,
                'profit': profit_trend
            }
            memberships = self.fuzzy_system.fuzzify(fuzzy_inputs)
            risk_level, action_level, confidence = self.fuzzy_system.infer(memberships)
            self.state_map.risk_level = risk_level
            self.state_map.action_level = action_level
            self.state_map.confidence = confidence
        # 自适应参数调整
        self.adjust_params()

    def calc_signal(self, kline: KLineData) -> None:
        """
        计算交易信号
        参数:
            kline: K线数据
        """
        if not self.hull_calculator.is_ready() or not self.stc_calculator.is_ready():
            return
        self.adjust_params()  # 先做自适应调整
        scores = self.fuse_signals()
        # 信号强度与风控参数联动
        self.adjust_risk_params(max(scores['buy'], scores['sell']))
        # 趋势过滤与顺势加仓
        trend = self.state_map.wave_type
        self.buy_signal = False
        self.sell_signal = False
        if trend == 'uptrend':
            if scores['buy'] > self.signal_threshold:
                self.buy_signal = True
                self.params_map.order_volume = 2
            else:
                self.params_map.order_volume = 1
        elif trend == 'downtrend':
            if scores['sell'] > self.signal_threshold:
                self.sell_signal = True
                self.params_map.order_volume = 2
            else:
                self.params_map.order_volume = 1
        else:  # consolidation or unknown
            if scores['buy'] > self.signal_threshold:
                self.buy_signal = True
                self.params_map.order_volume = 1
            if scores['sell'] > self.signal_threshold:
                self.sell_signal = True
                self.params_map.order_volume = 1
        log(f"最终信号: buy_signal={self.buy_signal}, sell_signal={self.sell_signal}, order_volume={self.params_map.order_volume}")
        # 根据交易方向调整信号
        if self.params_map.trade_direction == "buy":
            self.short_signal = self.sell_signal
            self.cover_signal = self.buy_signal
        else:
            self.short_signal = self.buy_signal
            self.cover_signal = self.sell_signal
        # 设置价格
        self.long_price = self.short_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def exec_signal(self) -> None:
        """执行交易信号"""
        self.signal_price = 0
        
        position = self.get_position(self.params_map.instrument_id)
        
        if self.order_id is not None:
            # 挂单未成交，撤单
            self.cancel_order(self.order_id)
        
        # 平仓逻辑
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
        
        # 开仓逻辑
        if self.short_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
        elif self.buy_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )